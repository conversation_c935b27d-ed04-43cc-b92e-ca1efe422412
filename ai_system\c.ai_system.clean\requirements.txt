# AI Systems: Clean & Consolidated Template Framework
# Core dependencies for the template system

# Data validation and modeling
pydantic>=2.0.0

# LLM integration and multi-model support
litellm>=1.0.0

# Async support
asyncio-compat>=0.1.0

# Optional: Enhanced CLI features
click>=8.0.0

# Optional: Advanced JSON handling
orjson>=3.8.0

# Development dependencies (optional)
# pytest>=7.0.0
# black>=22.0.0
# mypy>=1.0.0
# flake8>=5.0.0

# Documentation dependencies (optional)
# mkdocs>=1.4.0
# mkdocs-material>=8.0.0
