"""
AI Systems: Clean & Consolidated Template Framework
Core module initialization.

This module provides the foundational components for the AI template system,
including execution engine, data models, configuration, and validation.
"""

from .models import (
    ExecutorConfig,
    ModelResponse,
    InstructionResult,
    ExecutionResults,
    TemplateMetadata,
    SequenceStep,
    CatalogMetadata,
    TemplateCatalog
)

from .config import Config, PathUtils
from .executor import TemplateExecutor
from .catalog import CatalogManager
from .validation import TemplateValidator

__version__ = "1.0.0"
__author__ = "AI Systems Framework"

__all__ = [
    # Data Models
    "ExecutorConfig",
    "ModelResponse", 
    "InstructionResult",
    "ExecutionResults",
    "TemplateMetadata",
    "SequenceStep",
    "CatalogMetadata",
    "TemplateCatalog",
    
    # Core Components
    "Config",
    "PathUtils",
    "TemplateExecutor",
    "CatalogManager",
    "TemplateValidator",
]
