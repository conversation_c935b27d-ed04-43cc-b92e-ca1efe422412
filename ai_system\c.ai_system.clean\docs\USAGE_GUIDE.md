# Usage Guide: AI Template System

## Quick Start

### Installation

```bash
# Clone and setup
git clone <repository-url>
cd ai-system-clean
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### Basic Usage

```bash
# Execute a single template
python -m core execute 1010 "Extract a title from this research paper about quantum computing"

# Run a directional transformation
python -m core execute 9000 "This is a simple idea that needs amplification"

# Use specific models
python -m core execute 1031 "Classify this document structure" --models gpt-4o claude-3.5-sonnet

# Save results to file
python -m core execute 9003 "Distill the essence of this complex explanation" --output results.json
```

## Template Management

### Creating Templates

```bash
# Create a new template in Stage 1 (prototyping)
python -m core create "Content Synthesizer" \
  "Your goal is not to **combine** inputs, but to **synthesize** them into a unified whole." \
  "{role=synthesis_engine; input=[content:list]; process=[analyze_components(), identify_synergies(), merge_elements(), validate_coherence()]; output={synthesized:str}}"

# Create with test prompt
python -m core create "Data Analyzer" \
  "Your goal is not to **describe** data, but to **analyze** it for patterns and insights." \
  "{role=data_analysis_engine; input=[data:any]; process=[examine_structure(), identify_patterns(), extract_insights(), validate_findings()]; output={analysis:dict}}" \
  --testprompt "Analyze this sales data for trends"
```

### Catalog Management

```bash
# Generate catalog
python -m core catalog generate

# List all templates
python -m core catalog list

# List templates by stage
python -m core catalog list --stage stage1

# Show stage information
python -m core catalog info
```

### Template Validation

```bash
# Validate a template file
python -m core validate templates/stage1/1010-title_extractor.md

# Validate all templates in a directory
find templates/ -name "*.md" -exec python -m core validate {} \;
```

## Programmatic Usage

### Basic Execution

```python
from core import TemplateExecutor

# Create executor
executor = TemplateExecutor()

# Create configuration
config = executor.create_config(
    sequence_spec="1010",
    user_prompt="Extract title from this content",
    models=["gpt-4o", "claude-3.5-sonnet"]
)

# Execute
results = await executor.execute_sequence(config)

# Access results
for result in results.results:
    print(f"Step: {result.title}")
    for model, response in result.responses.items():
        print(f"{model}: {response.content}")
```

### Template Creation

```python
from core import CatalogManager, TemplateCreationRequest

# Create manager
manager = CatalogManager()

# Create template
request = TemplateCreationRequest(
    name="content_synthesizer",
    interpretation="Your goal is not to **combine** inputs, but to **synthesize** them...",
    transformation="{role=synthesis_engine; input=[content:list]; process=[...]; output={result:str}}",
    stage="stage1"
)

result = manager.create_template(request)
print(f"Created template: {result.template_id}")
```

### Catalog Operations

```python
from core import CatalogManager

manager = CatalogManager()

# Load catalog
catalog = manager.load_catalog()

# Get stage info
stage_info = manager.get_stage_info("stage1")
print(f"Next available ID: {stage_info.next_available_id}")

# List templates by stage
stage1_templates = manager.list_templates_by_stage("stage1")
```

## Advanced Features

### Sequence Execution

```bash
# Execute a sequence with multiple steps
python -m core execute "2001:a-c" "Transform this content through multiple stages"

# Chain mode (output of step N becomes input of step N+1)
python -m core execute "1010,9000,9003" "Process this text" --chain

# Disable chain mode for parallel processing
python -m core execute "1010,1031" "Analyze this content" --no-chain
```

### Model Configuration

```bash
# Use specific temperature
python -m core execute 9000 "Amplify this idea" --temperature 0.7

# Limit token usage
python -m core execute 1010 "Extract title" --max-tokens 100

# Use multiple models for comparison
python -m core execute 9003 "Distill this" --models gpt-4o claude-3.5-sonnet gemini-2.5-pro
```

### Output Control

```bash
# Hide system instructions
python -m core execute 1010 "Extract title" --no-instructions

# Hide responses (useful for batch processing)
python -m core execute 9000 "Amplify" --no-responses

# Minified JSON output
python -m core execute 1031 "Classify" --output results.json --minified
```

## Stage-Based Workflow

### Stage 1: Prototyping (1000-1999)
- **Purpose**: Rapid experimentation and iteration
- **ID Assignment**: Automatic
- **Validation**: Basic compliance checking

```bash
# Create prototype template
python -m core create "experimental_analyzer" \
  "Your goal is not to **examine**, but to **analyze** deeply..." \
  "{role=deep_analyzer; input=[content:any]; process=[...]; output={analysis:dict}}"
```

### Stage 2: Candidates (2000-2999)
- **Purpose**: Validated templates ready for testing
- **ID Assignment**: Guided placement
- **Validation**: Enhanced compliance and testing

```bash
# Promote from Stage 1 to Stage 2
# (This would be done programmatically)
```

### Stage 3: Production (3000-3999)
- **Purpose**: Battle-tested, immutable templates
- **ID Assignment**: Manual precision control
- **Validation**: Comprehensive testing and approval

### Directional Vectors (9000-9999)
- **Purpose**: Universal transformation operators
- **Types**: Expand (amplify, clarify) vs Compress (distill, extract)

```bash
# Use directional vectors
python -m core execute 9000 "Amplify this weak argument"
python -m core execute 9003 "Distill this verbose explanation"
```

## Best Practices

### Template Design
1. **Follow the three-part structure** exactly
2. **Use goal negation pattern** in interpretation
3. **Specify typed parameters** in transformation
4. **Avoid forbidden language** patterns
5. **Test with validation** before deployment

### Execution Patterns
1. **Start with single templates** before sequences
2. **Use chain mode** for transformation pipelines
3. **Compare multiple models** for critical tasks
4. **Save results** for analysis and debugging
5. **Monitor costs** and token usage

### Development Workflow
1. **Prototype in Stage 1** with automatic IDs
2. **Validate thoroughly** before promotion
3. **Test with real data** in Stage 2
4. **Deploy to Stage 3** only after approval
5. **Use directional vectors** for universal operations

## Troubleshooting

### Common Issues

**Template Validation Errors**:
```bash
# Check validation details
python -m core validate templates/stage1/problematic-template.md
```

**Execution Failures**:
```bash
# Check model availability
python -c "from core import Config; print(Config.get_available_models())"
```

**Catalog Issues**:
```bash
# Regenerate catalog
python -m core catalog generate
```

### Error Messages

- **"Template not found"**: Check sequence specification and catalog
- **"Validation failed"**: Review template against specification
- **"Model unavailable"**: Verify API keys and model access
- **"Sequence resolution failed"**: Check sequence format and template existence

### Getting Help

1. **Check documentation** in `docs/` directory
2. **Validate templates** before execution
3. **Review examples** in `examples/` directory
4. **Check system status** with catalog info command
