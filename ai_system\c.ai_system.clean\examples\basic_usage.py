#!/usr/bin/env python3
"""
Basic usage examples for the AI Template System.

This script demonstrates the core functionality of the clean,
consolidated AI template framework.
"""

import asyncio
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from core import (
    TemplateExecutor, CatalogManager, TemplateValidator,
    TemplateCreationRequest, Config
)


async def example_basic_execution():
    """Demonstrate basic template execution."""
    print("=" * 60)
    print("EXAMPLE 1: Basic Template Execution")
    print("=" * 60)
    
    executor = TemplateExecutor()
    
    # Create configuration for title extraction
    config = executor.create_config(
        sequence_spec="1010",
        user_prompt="This is a comprehensive research paper about the applications of quantum computing in cryptography and its implications for modern security systems.",
        models=["gpt-4o"]  # Using single model for demo
    )
    
    print(f"Executing template: {config.sequence_id}")
    print(f"Prompt: {config.user_prompt}")
    print()
    
    try:
        # Execute the template
        results = await executor.execute_sequence(config)
        
        # Display results
        for result in results.results:
            print(f"Template: {result.title}")
            for model, response in result.responses.items():
                print(f"Model: {model}")
                print(f"Response: {response.content}")
                print(f"Tokens: {response.tokens_used}, Cost: ${response.cost:.4f}")
                print()
    
    except Exception as e:
        print(f"Execution failed: {e}")


async def example_directional_vectors():
    """Demonstrate directional vector usage."""
    print("=" * 60)
    print("EXAMPLE 2: Directional Vector Transformations")
    print("=" * 60)
    
    executor = TemplateExecutor()
    
    # Test content
    content = "AI is useful for automation."
    
    # Amplify (expand direction)
    print("AMPLIFY (Expand Direction):")
    config_amplify = executor.create_config(
        sequence_spec="9000",
        user_prompt=content,
        models=["gpt-4o"]
    )
    
    try:
        results = await executor.execute_sequence(config_amplify)
        for result in results.results:
            for model, response in result.responses.items():
                print(f"Original: {content}")
                print(f"Amplified: {response.content}")
                print()
    except Exception as e:
        print(f"Amplify failed: {e}")
    
    # Distill (compress direction)
    print("DISTILL (Compress Direction):")
    verbose_content = "Artificial Intelligence represents a transformative technological paradigm that fundamentally revolutionizes the way we approach automation, decision-making, and problem-solving across numerous domains and industries."
    
    config_distill = executor.create_config(
        sequence_spec="9003",
        user_prompt=verbose_content,
        models=["gpt-4o"]
    )
    
    try:
        results = await executor.execute_sequence(config_distill)
        for result in results.results:
            for model, response in result.responses.items():
                print(f"Original: {verbose_content}")
                print(f"Distilled: {response.content}")
                print()
    except Exception as e:
        print(f"Distill failed: {e}")


def example_template_creation():
    """Demonstrate template creation and validation."""
    print("=" * 60)
    print("EXAMPLE 3: Template Creation and Validation")
    print("=" * 60)
    
    catalog_manager = CatalogManager()
    validator = TemplateValidator()
    
    # Create a new template
    request = TemplateCreationRequest(
        name="concept_mapper",
        interpretation="Your goal is not to **list** concepts, but to **map** their relationships and hierarchical structure.",
        transformation="{role=concept_mapping_engine; input=[content:str]; process=[identify_concepts(), analyze_relationships(), create_hierarchy(), validate_structure()]; constraints=[maintain_logical_flow(), preserve_semantic_meaning()]; requirements=[clear_hierarchy(), relationship_accuracy()]; output={concept_map:dict}}",
        stage="stage1",
        category="mapper",
        testprompt="Map the concepts in this text about machine learning algorithms."
    )
    
    print("Creating template...")
    result = catalog_manager.create_template(request)
    
    if result.success:
        print(f"✅ Template created successfully!")
        print(f"ID: {result.template_id}")
        print(f"File: {result.file_path}")
        print(f"Validation Score: {result.validation_result.compliance_score:.2f}")
        
        if result.validation_result.warnings:
            print("Warnings:")
            for warning in result.validation_result.warnings:
                print(f"  - {warning}")
        
        # Validate the created template
        print("\nValidating created template...")
        with open(result.file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        validation = validator.validate_template(content, result.template_id)
        print(f"Validation: {'✅ PASSED' if validation.is_valid else '❌ FAILED'}")
        
        if validation.errors:
            print("Errors:")
            for error in validation.errors:
                print(f"  - {error}")
    
    else:
        print(f"❌ Template creation failed: {result.message}")


def example_catalog_management():
    """Demonstrate catalog management operations."""
    print("=" * 60)
    print("EXAMPLE 4: Catalog Management")
    print("=" * 60)
    
    catalog_manager = CatalogManager()
    
    # Load catalog
    print("Loading catalog...")
    catalog = catalog_manager.load_catalog()
    
    print(f"Total templates: {catalog.catalog_meta.total_templates}")
    print(f"Total sequences: {catalog.catalog_meta.total_sequences}")
    print(f"Stages: {catalog.catalog_meta.stages}")
    print()
    
    # Show stage information
    print("Stage Information:")
    for stage_name in Config.STAGE_CONFIG.keys():
        try:
            stage_info = catalog_manager.get_stage_info(stage_name)
            print(f"  {stage_info.stage_name} ({stage_name}):")
            print(f"    Range: {stage_info.id_range}")
            print(f"    Templates: {stage_info.template_count}")
            print(f"    Next ID: {stage_info.next_available_id}")
            print()
        except Exception as e:
            print(f"    Error getting info for {stage_name}: {e}")
    
    # List templates by stage
    print("Templates by Stage:")
    for stage_name in ["stage1", "directional"]:
        templates = catalog_manager.list_templates_by_stage(stage_name)
        print(f"  {stage_name}: {len(templates)} templates")
        for template in templates[:3]:  # Show first 3
            print(f"    - {template.title}")
        if len(templates) > 3:
            print(f"    ... and {len(templates) - 3} more")
        print()


def example_validation_showcase():
    """Demonstrate template validation capabilities."""
    print("=" * 60)
    print("EXAMPLE 5: Template Validation Showcase")
    print("=" * 60)
    
    validator = TemplateValidator()
    
    # Valid template
    valid_template = """[Content Analyzer] Your goal is not to **describe** the content superficially, but to **analyze** its structure, themes, and effectiveness systematically. Execute as: `{role=content_analyst; input=[content:any]; process=[parse_structure(), identify_themes(), evaluate_effectiveness(), assess_clarity(), generate_insights()]; constraints=[objective_analysis(), evidence_based_conclusions()]; requirements=[comprehensive_coverage(), actionable_insights()]; output={analysis:dict}}`"""
    
    print("Validating VALID template:")
    result = validator.validate_template(valid_template, "test-valid")
    print(f"Valid: {result.is_valid}")
    print(f"Score: {result.compliance_score:.2f}")
    print()
    
    # Invalid template (missing goal negation)
    invalid_template = """[Bad Template] Please analyze the content for me. Execute as: `{role=assistant; input=[content]; process=[analyze()]; output={result}}`"""
    
    print("Validating INVALID template:")
    result = validator.validate_template(invalid_template, "test-invalid")
    print(f"Valid: {result.is_valid}")
    print(f"Score: {result.compliance_score:.2f}")
    print("Errors:")
    for error in result.errors:
        print(f"  - {error}")
    print()


async def main():
    """Run all examples."""
    print("AI Template System - Clean & Consolidated Framework")
    print("Example Usage Demonstrations")
    print()
    
    # Check if we can run examples
    try:
        # Run examples
        await example_basic_execution()
        await example_directional_vectors()
        example_template_creation()
        example_catalog_management()
        example_validation_showcase()
        
        print("=" * 60)
        print("All examples completed successfully!")
        print("=" * 60)
        
    except Exception as e:
        print(f"Example execution failed: {e}")
        print("\nNote: Some examples may fail if LiteLLM is not properly configured")
        print("or if API keys are not available. This is expected in demo mode.")


if __name__ == "__main__":
    asyncio.run(main())
