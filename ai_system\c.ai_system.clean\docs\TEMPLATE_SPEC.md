# Template Specification: Complete Syntax and Rules

## Core Template Structure

Every template MUST follow this exact format:

```
[Title] Interpretation Execute as: `{Transformation}`
```

This three-part structure is **mandatory** and **non-negotiable** for all templates.

## Part 1: [Title] - Template Identifier

### Format Requirements
- **Enclosure**: Must be enclosed in square brackets `[Title]`
- **Style**: Title case, descriptive, action-oriented
- **Content**: Concise description of template's function
- **Restrictions**: No generic terms like "Template" or "Instruction"

### Examples
```
[Essence Distillation]
[Code Optimizer]
[Content Analyzer]
[Form Classifier]
[Directional Translator]
```

### Invalid Examples
```
[Template]          # Too generic
[Helper]            # Non-specific
[AI Assistant]      # Conversational
[instruction]       # Wrong case
Title               # Missing brackets
```

## Part 2: Interpretation - Human-Readable Instructions

### Goal Negation Pattern (MANDATORY)
Must begin with: `"Your goal is not to **[action]**, but to **[transformation]**"`

### Structure Requirements
- **Command Voice**: No first-person references (I, me, my, we, us)
- **No Conversational Language**: No please, thank you, let's
- **No Uncertainty**: No maybe, perhaps, might, could
- **Clear Directives**: Specific, actionable instructions
- **Ending**: Must end with "Execute as:" leading to transformation block

### Valid Examples
```
Your goal is not to **analyze** the input, but to **amplify** its inherent qualities through intensification.

Your goal is not to **summarize** the content, but to **distill** its absolute essence through pure extraction.

Your goal is not to **describe** the code, but to **optimize** it for readability and performance while maintaining functionality.
```

## Part 3: `{Transformation}` - Machine-Parsable Parameters

### Format Requirements
- **Enclosure**: Must be enclosed in backticks with curly braces `{...}`
- **Structure**: Semicolon-separated key-value pairs
- **Components**: role, input, process, constraints, requirements, output

### Complete Structure
```
{
  role=<specific_role_name>;
  input=[<parameter_name>:<data_type>];
  process=[<step1>(), <step2>(), <step3>()];
  constraints=[<limitation1>(), <limitation2>()];
  requirements=[<requirement1>(), <requirement2>()];
  output={<result_name>:<data_type>}
}
```

## Component Specifications

### role (Required)
**Purpose**: Defines the functional role of the template

**Format**: `role=<specific_role_name>`

**Rules**:
- Must be specific and descriptive
- No generic roles like "assistant" or "helper"
- Use underscore_case for multi-word roles
- Should reflect the transformation being performed

**Valid Examples**:
```
role=essence_distiller
role=code_optimizer
role=content_analyst
role=form_classifier
role=amplification_operator
```

### input (Required)
**Purpose**: Specifies expected input format and parameters

**Format**: `input=[<parameter_name>:<data_type>]`

**Rules**:
- Use descriptive parameter names
- Include data type specification
- Support multiple parameters with comma separation
- Use standard data types: str, int, float, bool, any, list, dict

**Valid Examples**:
```
input=[text:str]
input=[code:str, language:str]
input=[original:any]
input=[content:str, options:dict]
```

### process (Required)
**Purpose**: Defines ordered processing steps

**Format**: `process=[<step1>(), <step2>(), ...]`

**Rules**:
- Use function-like notation with parentheses
- Steps must be actionable and atomic
- Maintain logical sequence order
- Use descriptive, verb-based names

**Valid Examples**:
```
process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(), validate_essence_preservation()]

process=[analyze_structure(), identify_inefficiencies(), apply_best_practices(), validate_functionality()]
```

### constraints (Optional)
**Purpose**: Specifies limitations and boundaries

**Format**: `constraints=[<constraint1>(), <constraint2>(), ...]`

**Valid Examples**:
```
constraints=[preserve_original_meaning(), maintain_technical_accuracy(), limit_output_length(max_words=100)]

constraints=[preserve_fundamental_nature(), maintain_structural_integrity()]
```

### requirements (Optional)
**Purpose**: Defines mandatory output characteristics

**Format**: `requirements=[<requirement1>(), <requirement2>(), ...]`

**Valid Examples**:
```
requirements=[structured_output(), type_safety(), comprehensive_coverage()]

requirements=[improved_readability(), measurable_performance_gains()]
```

### output (Required)
**Purpose**: Specifies return format and structure

**Format**: `output={<parameter_name>:<data_type>}`

**Valid Examples**:
```
output={enhanced_prompt:str}
output={analysis:dict}
output={optimized_code:str, improvements:list}
output={distilled_essence:any}
```

## Stage-Based Organization

### Stage 1 (1000-1999): Prototyping
- **Purpose**: Frictionless prototyping with auto-ID assignment
- **Characteristics**: Experimental, rapid iteration, automatic organization
- **ID Assignment**: Automatic within category ranges

### Stage 2 (2000-2999): Candidates
- **Purpose**: Validated candidates with guided placement
- **Characteristics**: Tested, refined, ready for evaluation
- **ID Assignment**: Guided placement based on validation

### Stage 3 (3000-3999): Production
- **Purpose**: Production templates with manual precision control
- **Characteristics**: Immutable, battle-tested, precisely controlled
- **ID Assignment**: Manual precision placement

### Directional Vectors (9000-9999)
- **Purpose**: Pure transformation operators
- **Characteristics**: Universal applicability, direction-specific
- **Types**: Expand (amplify, clarify) vs Compress (distill, extract)

## File Naming Convention

### Format
```
<sequence_id>-<step>-<descriptive_name>.md
```

### Examples
**Standalone Templates**:
```
1010-title_extractor.md
1031-form_classifier.md
3001-production_analyzer.md
```

**Sequential Templates**:
```
2001-a-essence_distillation.md
2001-b-coherence_enhancement.md
2001-c-precision_optimization.md
```

**Directional Vectors**:
```
9000-a-amplify.md
9001-a-clarify.md
9003-b-distill.md
```

## Validation Rules

### Mandatory Compliance Checklist
- [ ] **Structure**: Three-part structure intact (Title, Interpretation, Transformation)
- [ ] **Goal Negation**: Present and properly formatted
- [ ] **Role**: Specific and non-generic
- [ ] **Input**: Parameters typed with descriptive names
- [ ] **Process**: Steps ordered, atomic, actionable with function notation
- [ ] **Output**: Format typed and structured
- [ ] **Language**: No forbidden language patterns
- [ ] **Naming**: File naming convention followed

### Forbidden Practices
**Language Violations**:
- First-person references: I, me, my, we, us
- Conversational phrases: please, thank you, let's
- Uncertainty language: maybe, perhaps, might
- Question forms: Would you like...?

**Structural Violations**:
- Merging sections or omitting required parts
- Untyped parameters or vague names
- Generic roles or non-actionable steps

## Integration Guidelines

Templates integrate with the catalog system through:
- Automatic metadata extraction
- Stage-based organization
- Validation compliance checking
- Dynamic template discovery

For successful integration, templates must strictly adhere to this specification.
