"""
Configuration management for the AI template system.

This module handles LLM provider/model selection, path utilities,
and system-wide configuration settings.
"""

import os
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional


class PathUtils:
    """Utilities for file path handling and directory management."""

    @staticmethod
    def get_script_dir():
        """Get current script directory."""
        return os.path.dirname(os.path.abspath(__file__))

    @staticmethod
    def get_project_root():
        """Get project root directory."""
        return os.path.dirname(PathUtils.get_script_dir())

    @staticmethod
    def join_path(*parts):
        """Join path components."""
        return os.path.join(*parts)

    @staticmethod
    def normalize_path_for_display(path):
        """Convert path to use forward slashes for display purposes."""
        if path is None:
            return None
        return str(path).replace('\\', '/')

    @staticmethod
    def get_relative_path_for_display(path):
        """Convert absolute path to relative path for display purposes."""
        if path is None:
            return None

        abs_path = os.path.abspath(path)
        cwd = os.getcwd()

        try:
            rel_path = os.path.relpath(abs_path, cwd)
            return rel_path.replace('\\', '/')
        except ValueError:
            return PathUtils.normalize_path_for_display(abs_path)

    @staticmethod
    def ensure_dir_exists(directory_path):
        """Create directory if it doesn't exist."""
        if not os.path.exists(directory_path):
            try:
                os.makedirs(directory_path)
                print(f"Created directory: {PathUtils.normalize_path_for_display(directory_path)}")
                return True
            except OSError as e:
                print(f"Error creating directory '{PathUtils.normalize_path_for_display(directory_path)}': {e}", file=sys.stderr)
                raise
        return True

    @staticmethod
    def sanitize_filename(filename):
        """Replace invalid filename characters."""
        sanitized = filename
        replacements = {
            "|": "+", ":": "-", "?": "_", "*": "_",
            "<": "_", ">": "_", "\"": "_", "/": "_", "\\": "_"
        }

        for char, replacement in replacements.items():
            sanitized = sanitized.replace(char, replacement)

        return sanitized

    @staticmethod
    def generate_output_filename(sequence_id, source_type, models, timestamp=None):
        """Generate standardized output filename."""
        sanitized_sequence_id = PathUtils.sanitize_filename(sequence_id)
        display_sequence_id = f"{source_type}-{sanitized_sequence_id}"

        if timestamp is None:
            timestamp = datetime.now().strftime("%Y.%m.%d-kl.%H.%M.%S")

        model_tag = "_".join(models).replace('/', '-').replace(':', '-')[:30]

        return f"history--{timestamp}--{display_sequence_id}--{model_tag}.json"


class Config:
    """Manages LLM provider/model selection and system settings."""

    # Environment Setup
    @staticmethod
    def _ensure_utf8_encoding():
        """Set UTF-8 output encoding for terminals."""
        for stream in (sys.stdout, sys.stderr):
            if hasattr(stream, "reconfigure"):
                try:
                    stream.reconfigure(encoding="utf-8", errors="replace")
                except Exception:
                    pass

    # Directory Configuration
    PROJECT_ROOT = PathUtils.get_project_root()
    DEFAULT_OUTPUT_DIR = PathUtils.join_path(PROJECT_ROOT, "output")
    TEMPLATES_DIR = PathUtils.join_path(PROJECT_ROOT, "templates")
    DOCS_DIR = PathUtils.join_path(PROJECT_ROOT, "docs")
    EXAMPLES_DIR = PathUtils.join_path(PROJECT_ROOT, "examples")

    # Stage Configuration
    STAGE_CONFIG = {
        "stage1": {
            "name": "Prototyping",
            "id_range": (1000, 1999),
            "description": "Frictionless prototyping with auto-ID assignment",
            "directory": "stage1"
        },
        "stage2": {
            "name": "Candidates", 
            "id_range": (2000, 2999),
            "description": "Validated candidates with guided placement",
            "directory": "stage2"
        },
        "stage3": {
            "name": "Production",
            "id_range": (3000, 3999),
            "description": "Production templates with manual precision control",
            "directory": "stage3"
        },
        "directional": {
            "name": "Directional Vectors",
            "id_range": (9000, 9999),
            "description": "Pure transformation operators",
            "directory": "directional"
        }
    }

    # Model Registry (maps user-friendly names to actual LiteLLM model IDs)
    MODEL_REGISTRY = {
        # OpenAI
        "gpt-3.5-turbo": "gpt-3.5-turbo",
        "gpt-4": "gpt-4",
        "gpt-4-turbo": "gpt-4-turbo",
        "gpt-4o": "gpt-4o",
        "gpt-4o-mini": "gpt-4o-mini",
        "o3-mini": "o3-mini",
        
        # Anthropic
        "claude-3-opus": "anthropic/claude-3-opus-20240229",
        "claude-3-sonnet": "anthropic/claude-3-sonnet-20240229",
        "claude-3-haiku": "anthropic/claude-3-haiku-20240307",
        "claude-3.5-sonnet": "anthropic/claude-3-5-sonnet-20241022",
        
        # Google
        "gemini-pro": "gemini/gemini-1.5-pro",
        "gemini-flash": "gemini/gemini-1.5-flash-latest",
        "gemini-2-flash": "gemini/gemini-2.0-flash",
        "gemini-2.5-pro": "gemini/gemini-2.5-pro-preview-03-25",
        
        # Deepseek
        "deepseek-reasoner": "deepseek/deepseek-reasoner",
        "deepseek-coder": "deepseek/deepseek-coder",
        "deepseek-chat": "deepseek/deepseek-chat",
    }

    # Provider Configuration
    MODEL_CONFIG = {
        "openai": {
            "models": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-3.5-turbo", "o3-mini"],
            "default": "gpt-4o",
            "fallback": "gpt-3.5-turbo"
        },
        "anthropic": {
            "models": ["claude-3.5-sonnet", "claude-3-opus", "claude-3-sonnet", "claude-3-haiku"],
            "default": "claude-3.5-sonnet",
            "fallback": "claude-3-haiku"
        },
        "google": {
            "models": ["gemini-2.5-pro", "gemini-2-flash", "gemini-pro", "gemini-flash"],
            "default": "gemini-2.5-pro",
            "fallback": "gemini-flash"
        },
        "deepseek": {
            "models": ["deepseek-chat", "deepseek-reasoner", "deepseek-coder"],
            "default": "deepseek-chat",
            "fallback": "deepseek-chat"
        }
    }

    # Default Settings
    DEFAULT_PROVIDER = "openai"
    DEFAULT_MODELS = ["gpt-4o", "claude-3.5-sonnet"]

    @classmethod
    def get_stage_info(cls, stage: str) -> Dict[str, Any]:
        """Get information about a specific stage."""
        return cls.STAGE_CONFIG.get(stage, {})

    @classmethod
    def get_stage_directory(cls, stage: str) -> str:
        """Get directory path for a stage."""
        stage_info = cls.get_stage_info(stage)
        if not stage_info:
            raise ValueError(f"Unknown stage: {stage}")
        return PathUtils.join_path(cls.TEMPLATES_DIR, stage_info["directory"])

    @classmethod
    def get_default_model(cls, provider=None):
        """Get default model for a provider."""
        provider = provider or cls.DEFAULT_PROVIDER
        if provider in cls.MODEL_CONFIG:
            return cls.MODEL_CONFIG[provider]["default"]
        return cls.MODEL_CONFIG["openai"]["default"]

    @classmethod
    def get_model_params(cls, model_name=None, provider=None):
        """Resolve model name to LiteLLM ID with parameters."""
        provider = provider or cls.DEFAULT_PROVIDER
        model_name = model_name or cls.get_default_model(provider)
        
        actual_model_id = cls.MODEL_REGISTRY.get(model_name, model_name)
        return {"model": actual_model_id}

    @classmethod
    def get_available_models(cls):
        """Get models grouped by provider."""
        result = {}
        for provider, config in cls.MODEL_CONFIG.items():
            default_model = config["default"]
            provider_models = []
            
            for model_name in config["models"]:
                model_id = cls.MODEL_REGISTRY.get(model_name, model_name)
                provider_models.append({
                    "name": model_name,
                    "model_id": model_id,
                    "is_default": (model_name == default_model or model_id == default_model)
                })
            
            result[provider] = provider_models
        
        return result

    @classmethod
    def configure_litellm(cls):
        """Configure LiteLLM settings."""
        try:
            import litellm
            litellm.drop_params = True
            litellm.num_retries = 3
            litellm.request_timeout = 120
            litellm.set_verbose = False
            litellm.callbacks = []
        except ImportError:
            print("Warning: LiteLLM not available. Install with: pip install litellm")
        
        cls._ensure_utf8_encoding()

    @classmethod
    def set_default_output_dir(cls, directory: str):
        """Set default output directory."""
        cls.DEFAULT_OUTPUT_DIR = directory
