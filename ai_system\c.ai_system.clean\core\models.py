"""
Data structures and models for the AI instruction template system.

This module defines Pydantic models for configuration, execution results,
and other data structures used throughout the system. These models ensure
type safety and data validation across all system components.
"""

from typing import Dict, List, Any, Optional, Callable
from pydantic import BaseModel, Field
from datetime import datetime


class ExecutorConfig(BaseModel):
    """Configuration for sequence execution."""
    
    # Sequence and input parameters
    sequence_steps: List[tuple] = Field(description="Step ID and template data tuples")
    user_prompt: str = Field(description="User input prompt")
    sequence_id: str = Field(description="Sequence identifier")
    models: List[str] = Field(description="Models to use")

    # Output parameters
    output_file: Optional[str] = Field(default=None, description="Output JSON file path")
    minified_output: bool = Field(default=False, description="Minify output")

    # Display options
    show_inputs: bool = Field(default=True, description="Show input prompts")
    show_system_instructions: bool = Field(default=True, description="Show system instructions")
    show_responses: bool = Field(default=True, description="Show responses")

    # Execution options
    chain_mode: bool = Field(default=True, description="Use chain mode")
    aggregator: Optional[str] = Field(default=None, description="Aggregator template ID")
    aggregator_inputs: Optional[List[str]] = Field(default=None, description="Step IDs for aggregation")

    # Stage management
    stage: Optional[str] = Field(default="stage1", description="Template stage (stage1, stage2, stage3)")
    step_offset: int = Field(default=0, description="Step numbering offset")
    is_aggregator: bool = Field(default=False, description="Is aggregator sequence")

    # System components
    system_instruction_extractor: Callable[[Any], str] = Field(description="Instruction extractor")

    # Model parameters
    temperature: Optional[float] = Field(default=None, description="Temperature")
    max_tokens: Optional[int] = Field(default=None, description="Max tokens")

    class Config:
        arbitrary_types_allowed = True


class ModelResponse(BaseModel):
    """Model response data."""
    model: str = Field(description="Model used")
    content: str = Field(description="Response content")
    tokens_used: Optional[int] = Field(default=None, description="Tokens consumed")
    cost: Optional[float] = Field(default=None, description="Estimated cost")
    response_time: Optional[float] = Field(default=None, description="Response time in seconds")


class InstructionResult(BaseModel):
    """Results for one instruction step."""
    instruction: str = Field(description="Instruction used")
    step: str = Field(description="Step identifier")
    title: str = Field(description="Instruction title")
    stage: str = Field(description="Template stage")
    responses: Dict[str, ModelResponse] = Field(description="Model responses")
    execution_time: Optional[float] = Field(default=None, description="Step execution time")


class ExecutionResults(BaseModel):
    """Complete sequence execution results."""
    user_prompt: str = Field(description="Initial prompt")
    sequence_id: str = Field(description="Sequence ID")
    results: List[InstructionResult] = Field(description="Step results")
    total_cost: Optional[float] = Field(default=None, description="Total execution cost")
    total_time: Optional[float] = Field(default=None, description="Total execution time")
    timestamp: datetime = Field(default_factory=datetime.now, description="Execution timestamp")


class TemplateMetadata(BaseModel):
    """Metadata for a template."""
    title: str = Field(description="Template title")
    interpretation: str = Field(description="Human-readable interpretation")
    transformation: str = Field(description="Machine-parsable transformation")
    keywords: Optional[str] = Field(default="", description="Extracted keywords")
    stage: str = Field(description="Template stage (stage1, stage2, stage3)")
    category: Optional[str] = Field(default=None, description="Template category")
    direction: Optional[str] = Field(default=None, description="Transformation direction (expand/compress)")
    raw: str = Field(description="Raw template content")
    file_path: Optional[str] = Field(default=None, description="Source file path")
    created_at: Optional[datetime] = Field(default=None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(default=None, description="Last update timestamp")


class SequenceStep(BaseModel):
    """A step in a template sequence."""
    template_id: str = Field(description="Template identifier")
    step: str = Field(description="Step letter (a, b, c, etc.)")
    order: int = Field(description="Execution order")
    stage: str = Field(description="Template stage")


class CatalogMetadata(BaseModel):
    """Metadata for a template catalog."""
    level: str = Field(description="Template level")
    format: str = Field(description="Template format")
    generated_at: str = Field(description="Generation timestamp")
    source_directory: str = Field(description="Source directory")
    total_templates: int = Field(description="Total number of templates")
    total_sequences: int = Field(description="Total number of sequences")
    stages: Dict[str, int] = Field(description="Templates per stage")


class TemplateCatalog(BaseModel):
    """Complete template catalog structure."""
    catalog_meta: CatalogMetadata = Field(description="Catalog metadata")
    templates: Dict[str, TemplateMetadata] = Field(description="Template definitions")
    sequences: Dict[str, List[SequenceStep]] = Field(description="Sequence definitions")


class ValidationResult(BaseModel):
    """Template validation result."""
    is_valid: bool = Field(description="Overall validation status")
    template_id: str = Field(description="Template identifier")
    errors: List[str] = Field(default_factory=list, description="Validation errors")
    warnings: List[str] = Field(default_factory=list, description="Validation warnings")
    compliance_score: float = Field(description="Compliance score (0.0-1.0)")
    
    
class StageInfo(BaseModel):
    """Information about a template stage."""
    stage_name: str = Field(description="Stage name")
    id_range: tuple = Field(description="ID range for this stage")
    description: str = Field(description="Stage description")
    template_count: int = Field(description="Number of templates in stage")
    next_available_id: Optional[int] = Field(default=None, description="Next available ID")


class TemplateCreationRequest(BaseModel):
    """Request for creating a new template."""
    name: str = Field(description="Template name")
    interpretation: str = Field(description="Human-readable interpretation")
    transformation: str = Field(description="Machine-parsable transformation")
    stage: str = Field(default="stage1", description="Target stage")
    category: Optional[str] = Field(default=None, description="Template category")
    testprompt: Optional[str] = Field(default=None, description="Test prompt for validation")


class TemplateCreationResult(BaseModel):
    """Result of template creation."""
    success: bool = Field(description="Creation success status")
    template_id: str = Field(description="Assigned template ID")
    file_path: str = Field(description="Created file path")
    validation_result: ValidationResult = Field(description="Validation results")
    message: str = Field(description="Creation message")
