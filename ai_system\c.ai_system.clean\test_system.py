#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive test suite for the AI Template System.

This script tests all core components to ensure they work as intended.
"""

import os
import sys
import json
import tempfile
from pathlib import Path

# Ensure UTF-8 encoding for output
if hasattr(sys.stdout, 'reconfigure'):
    try:
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')
    except Exception:
        pass

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """Test that all core modules can be imported."""
    print("Testing imports...")
    
    try:
        from core import (
            TemplateExecutor, CatalogManager, TemplateValidator,
            Config, PathUtils, TemplateCreationRequest, TemplateCreationResult,
            ExecutorConfig, ModelResponse, InstructionResult, ExecutionResults,
            TemplateMetadata, SequenceStep, CatalogMetadata, TemplateCatalog,
            ValidationResult, StageInfo
        )
        print("[PASS] All imports successful")
        return True
    except ImportError as e:
        print(f"[FAIL] Import failed: {e}")
        return False

def test_config():
    """Test configuration management."""
    print("\nTesting configuration...")
    
    try:
        from core import Config, PathUtils
        
        # Test basic config
        assert Config.DEFAULT_PROVIDER == "openai"
        assert len(Config.MODEL_REGISTRY) > 0
        assert len(Config.STAGE_CONFIG) == 4
        
        # Test model configuration
        models = Config.get_available_models()
        assert "openai" in models
        assert "anthropic" in models
        
        default_model = Config.get_default_model()
        assert default_model is not None
        
        model_params = Config.get_model_params("gpt-4o")
        assert "model" in model_params
        
        # Test stage configuration
        stage_info = Config.get_stage_info("stage1")
        assert stage_info["id_range"] == (1000, 1999)
        
        # Test path utilities
        script_dir = PathUtils.get_script_dir()
        assert os.path.exists(script_dir)
        
        project_root = PathUtils.get_project_root()
        assert os.path.exists(project_root)
        
        # Test filename sanitization
        sanitized = PathUtils.sanitize_filename("test|file:name?.txt")
        assert "|" not in sanitized
        assert ":" not in sanitized
        
        print("[PASS] Configuration tests passed")
        return True

    except Exception as e:
        print(f"[FAIL] Configuration test failed: {e}")
        return False

def test_validation():
    """Test template validation."""
    print("\nTesting validation...")
    
    try:
        from core import TemplateValidator
        
        validator = TemplateValidator()
        
        # Test valid template
        valid_template = """[Title Extractor] Your goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title. Execute as: `{role=title_extractor; input=[text:str]; process=[identify_key_concepts(), synthesize_title()]; output={title:str}}`"""
        
        result = validator.validate_template(valid_template, "test-1010")
        assert result.is_valid == True
        assert result.compliance_score > 0.8
        assert len(result.errors) == 0
        
        # Test invalid template (missing goal negation)
        invalid_template = """[Bad Template] Please help me analyze this. Execute as: `{role=assistant; input=[text]; output={result}}`"""
        
        result = validator.validate_template(invalid_template, "test-bad")
        assert result.is_valid == False
        assert len(result.errors) > 0
        
        # Test metadata extraction
        metadata = validator.extract_metadata(valid_template, "test.md")
        assert metadata.title == "Title Extractor"
        assert "extract" in metadata.interpretation.lower()
        assert metadata.transformation.startswith("`{")
        
        print("[PASS] Validation tests passed")
        return True

    except Exception as e:
        print(f"[FAIL] Validation test failed: {e}")
        return False

def test_catalog_manager():
    """Test catalog management."""
    print("\nTesting catalog management...")
    
    try:
        from core import CatalogManager, TemplateCreationRequest
        
        manager = CatalogManager()
        
        # Test stage info
        stage_info = manager.get_stage_info("stage1")
        assert stage_info.stage_name == "Prototyping"
        assert stage_info.id_range == (1000, 1999)
        
        # Test catalog loading (should work even with empty directories)
        catalog = manager.load_catalog()
        assert catalog.catalog_meta.level == "clean"
        assert catalog.catalog_meta.format == "md"
        assert isinstance(catalog.templates, dict)
        assert isinstance(catalog.sequences, dict)
        
        print("[PASS] Catalog management tests passed")
        return True

    except Exception as e:
        print(f"[FAIL] Catalog management test failed: {e}")
        return False

def test_template_creation():
    """Test template creation in a temporary directory."""
    print("\nTesting template creation...")
    
    try:
        from core import CatalogManager, TemplateCreationRequest, Config
        
        # Create temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            # Override templates directory for testing
            original_templates_dir = Config.TEMPLATES_DIR
            Config.TEMPLATES_DIR = temp_dir
            
            # Create stage directories
            stage1_dir = os.path.join(temp_dir, "stage1")
            os.makedirs(stage1_dir, exist_ok=True)
            
            manager = CatalogManager()
            
            # Test template creation
            request = TemplateCreationRequest(
                name="test_analyzer",
                interpretation="Your goal is not to **describe** the input, but to **analyze** it systematically.",
                transformation="{role=test_analyzer; input=[content:str]; process=[analyze_structure(), extract_insights()]; output={analysis:dict}}",
                stage="stage1"
            )
            
            result = manager.create_template(request)
            
            assert result.success == True
            assert result.template_id == "1000"  # First available ID
            assert os.path.exists(result.file_path)
            
            # Verify file content
            with open(result.file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            assert "[Test_Analyzer]" in content
            assert "analyze" in content.lower()
            
            # Restore original config
            Config.TEMPLATES_DIR = original_templates_dir
        
        print("✅ Template creation tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Template creation test failed: {e}")
        return False

def test_executor_config():
    """Test executor configuration."""
    print("\nTesting executor configuration...")
    
    try:
        from core import TemplateExecutor
        
        executor = TemplateExecutor()
        
        # Test config creation (without actual execution)
        config = executor.create_config(
            sequence_spec="1010",
            user_prompt="Test prompt",
            models=["gpt-4o"],
            chain_mode=True
        )
        
        assert config.sequence_id == "1010"
        assert config.user_prompt == "Test prompt"
        assert config.models == ["gpt-4o"]
        assert config.chain_mode == True
        assert callable(config.system_instruction_extractor)
        
        print("✅ Executor configuration tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Executor configuration test failed: {e}")
        return False

def test_data_models():
    """Test Pydantic data models."""
    print("\nTesting data models...")
    
    try:
        from core.models import (
            ModelResponse, InstructionResult, ExecutionResults,
            TemplateMetadata, ValidationResult, StageInfo
        )
        from datetime import datetime
        
        # Test ModelResponse
        response = ModelResponse(
            model="gpt-4o",
            content="Test response",
            tokens_used=100,
            cost=0.01,
            response_time=1.5
        )
        assert response.model == "gpt-4o"
        assert response.tokens_used == 100
        
        # Test TemplateMetadata
        metadata = TemplateMetadata(
            title="Test Template",
            interpretation="Test interpretation",
            transformation="Test transformation",
            stage="stage1",
            raw="Raw content"
        )
        assert metadata.title == "Test Template"
        assert metadata.stage == "stage1"
        
        # Test ValidationResult
        validation = ValidationResult(
            is_valid=True,
            template_id="1010",
            compliance_score=0.95
        )
        assert validation.is_valid == True
        assert validation.compliance_score == 0.95
        
        print("✅ Data model tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Data model test failed: {e}")
        return False

def test_existing_templates():
    """Test existing template files."""
    print("\nTesting existing templates...")
    
    try:
        from core import TemplateValidator
        
        validator = TemplateValidator()
        templates_dir = Path(__file__).parent / "templates"
        
        if not templates_dir.exists():
            print("⚠️  No templates directory found, skipping template file tests")
            return True
        
        template_files = list(templates_dir.rglob("*.md"))
        
        if not template_files:
            print("⚠️  No template files found, skipping template file tests")
            return True
        
        valid_count = 0
        total_count = len(template_files)
        
        for template_file in template_files:
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                result = validator.validate_template(content, str(template_file))
                
                if result.is_valid:
                    valid_count += 1
                else:
                    print(f"⚠️  Template {template_file.name} has validation issues:")
                    for error in result.errors[:3]:  # Show first 3 errors
                        print(f"    - {error}")
            
            except Exception as e:
                print(f"⚠️  Error reading {template_file}: {e}")
        
        print(f"✅ Template validation: {valid_count}/{total_count} templates valid")
        return True
        
    except Exception as e:
        print(f"❌ Template file test failed: {e}")
        return False

def test_cli_interface():
    """Test CLI interface components."""
    print("\nTesting CLI interface...")
    
    try:
        # Test that CLI module can be imported
        from core.__main__ import main
        
        # Test argument parsing (without actually running)
        import argparse
        
        # This tests that the CLI structure is valid
        parser = argparse.ArgumentParser()
        subparsers = parser.add_subparsers(dest='command')
        
        # Add a test subcommand
        test_parser = subparsers.add_parser('test')
        test_parser.add_argument('--test-arg', help='Test argument')
        
        # Parse test arguments
        args = parser.parse_args(['test', '--test-arg', 'value'])
        assert args.command == 'test'
        assert args.test_arg == 'value'
        
        print("✅ CLI interface tests passed")
        return True
        
    except Exception as e:
        print(f"❌ CLI interface test failed: {e}")
        return False

def test_directory_structure():
    """Test that required directories exist or can be created."""
    print("\nTesting directory structure...")
    
    try:
        from core import Config, PathUtils
        
        # Test that we can create required directories
        test_dirs = [
            Config.TEMPLATES_DIR,
            Config.DOCS_DIR,
            Config.EXAMPLES_DIR,
            Config.DEFAULT_OUTPUT_DIR
        ]
        
        for dir_path in test_dirs:
            if not os.path.exists(dir_path):
                try:
                    PathUtils.ensure_dir_exists(dir_path)
                    print(f"[PASS] Created directory: {dir_path}")
                except Exception as e:
                    print(f"[WARN] Could not create directory {dir_path}: {e}")
            else:
                print(f"[PASS] Directory exists: {dir_path}")
        
        # Test stage directories
        for stage_name in Config.STAGE_CONFIG.keys():
            stage_dir = Config.get_stage_directory(stage_name)
            if not os.path.exists(stage_dir):
                try:
                    PathUtils.ensure_dir_exists(stage_dir)
                    print(f"[PASS] Created stage directory: {stage_dir}")
                except Exception as e:
                    print(f"[WARN] Could not create stage directory {stage_dir}: {e}")

        print("[PASS] Directory structure tests passed")
        return True

    except Exception as e:
        print(f"[FAIL] Directory structure test failed: {e}")
        return False

def run_all_tests():
    """Run all tests and report results."""
    print("AI Template System - Comprehensive Test Suite")
    print("=" * 60)
    
    tests = [
        ("Imports", test_imports),
        ("Configuration", test_config),
        ("Validation", test_validation),
        ("Catalog Manager", test_catalog_manager),
        ("Template Creation", test_template_creation),
        ("Executor Config", test_executor_config),
        ("Data Models", test_data_models),
        ("Existing Templates", test_existing_templates),
        ("CLI Interface", test_cli_interface),
        ("Directory Structure", test_directory_structure)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"[FAIL] {test_name} test crashed: {e}")
            failed += 1

    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"[PASS] Passed: {passed}")
    print(f"[FAIL] Failed: {failed}")
    print(f"[STATS] Success Rate: {passed/(passed+failed)*100:.1f}%")

    if failed == 0:
        print("\n[SUCCESS] All tests passed! The system is working correctly.")
    else:
        print(f"\n[WARN] {failed} test(s) failed. Please review the issues above.")
    
    return failed == 0

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
