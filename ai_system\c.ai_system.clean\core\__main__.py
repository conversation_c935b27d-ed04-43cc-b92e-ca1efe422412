#!/usr/bin/env python3
"""
Command-line interface for the AI Template System.

This module provides a CLI for executing templates, managing catalogs,
and interacting with the template system.
"""

import argparse
import asyncio
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from core import (
    TemplateExecutor, CatalogManager, TemplateValidator,
    Config, PathUtils, TemplateCreationRequest
)


async def execute_template(args):
    """Execute a template sequence."""
    executor = TemplateExecutor()
    
    try:
        # Create configuration
        config = executor.create_config(
            sequence_spec=args.sequence,
            user_prompt=args.prompt,
            models=args.models or Config.DEFAULT_MODELS,
            chain_mode=args.chain,
            show_inputs=args.show_inputs,
            show_system_instructions=args.show_instructions,
            show_responses=args.show_responses,
            temperature=args.temperature,
            max_tokens=args.max_tokens
        )
        
        print(f"Executing sequence: {args.sequence}")
        print(f"Models: {', '.join(config.models)}")
        print(f"Chain mode: {config.chain_mode}")
        print(f"Prompt: {args.prompt[:100]}...")
        print()
        
        # Execute sequence
        results = await executor.execute_sequence(config)
        
        # Save results if output file specified
        if args.output:
            executor.save_results(results, args.output, args.minified)
        
        # Display summary
        print(f"\n{'='*60}")
        print("EXECUTION SUMMARY")
        print(f"{'='*60}")
        print(f"Sequence: {results.sequence_id}")
        print(f"Steps: {len(results.results)}")
        print(f"Total Cost: ${results.total_cost:.4f}")
        print(f"Total Time: {results.total_time:.2f}s")
        print(f"Timestamp: {results.timestamp}")
        
    except Exception as e:
        print(f"Error executing template: {e}")
        sys.exit(1)


def manage_catalog(args):
    """Manage template catalog."""
    catalog_manager = CatalogManager()
    
    if args.catalog_action == "generate":
        print("Generating template catalog...")
        catalog = catalog_manager.load_catalog()
        output_path = catalog_manager.save_catalog_json(args.output)
        print(f"Catalog generated: {output_path}")
        print(f"Templates: {catalog.catalog_meta.total_templates}")
        print(f"Sequences: {catalog.catalog_meta.total_sequences}")
        
    elif args.catalog_action == "list":
        catalog = catalog_manager.load_catalog()
        
        if args.stage:
            templates = [t for t in catalog.templates.values() if t.stage == args.stage]
            print(f"Templates in {args.stage}:")
        else:
            templates = list(catalog.templates.values())
            print("All templates:")
        
        for template in sorted(templates, key=lambda x: x.title):
            print(f"  {template.title} ({template.stage})")
    
    elif args.catalog_action == "info":
        for stage_name in Config.STAGE_CONFIG.keys():
            stage_info = catalog_manager.get_stage_info(stage_name)
            print(f"{stage_info.stage_name} ({stage_name}):")
            print(f"  Range: {stage_info.id_range}")
            print(f"  Templates: {stage_info.template_count}")
            print(f"  Next ID: {stage_info.next_available_id}")
            print(f"  Description: {stage_info.description}")
            print()


def create_template(args):
    """Create a new template."""
    catalog_manager = CatalogManager()
    
    request = TemplateCreationRequest(
        name=args.name,
        interpretation=args.interpretation,
        transformation=args.transformation,
        stage=args.stage,
        category=args.category,
        testprompt=args.testprompt
    )
    
    result = catalog_manager.create_template(request)
    
    if result.success:
        print(f"Template created successfully!")
        print(f"ID: {result.template_id}")
        print(f"File: {result.file_path}")
        print(f"Validation Score: {result.validation_result.compliance_score:.2f}")
        
        if result.validation_result.warnings:
            print("Warnings:")
            for warning in result.validation_result.warnings:
                print(f"  - {warning}")
    else:
        print(f"Template creation failed: {result.message}")
        if result.validation_result.errors:
            print("Errors:")
            for error in result.validation_result.errors:
                print(f"  - {error}")


def validate_template(args):
    """Validate a template file."""
    validator = TemplateValidator()
    
    try:
        with open(args.file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        result = validator.validate_template(content, args.file)
        
        print(f"Validation Results for {args.file}:")
        print(f"Valid: {result.is_valid}")
        print(f"Compliance Score: {result.compliance_score:.2f}")
        
        if result.errors:
            print("\nErrors:")
            for error in result.errors:
                print(f"  - {error}")
        
        if result.warnings:
            print("\nWarnings:")
            for warning in result.warnings:
                print(f"  - {warning}")
        
        if result.is_valid:
            print("\n✅ Template is valid and compliant!")
        else:
            print("\n❌ Template has validation errors.")
            sys.exit(1)
            
    except FileNotFoundError:
        print(f"Error: File not found: {args.file}")
        sys.exit(1)
    except Exception as e:
        print(f"Error validating template: {e}")
        sys.exit(1)


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="AI Template System - Clean & Consolidated Framework",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Execute command
    execute_parser = subparsers.add_parser('execute', help='Execute a template sequence')
    execute_parser.add_argument('sequence', help='Sequence ID or specification')
    execute_parser.add_argument('prompt', help='User prompt')
    execute_parser.add_argument('--models', nargs='+', help='Models to use')
    execute_parser.add_argument('--no-chain', dest='chain', action='store_false', help='Disable chain mode')
    execute_parser.add_argument('--output', '-o', help='Output file path')
    execute_parser.add_argument('--minified', action='store_true', help='Minify JSON output')
    execute_parser.add_argument('--temperature', type=float, help='Temperature setting')
    execute_parser.add_argument('--max-tokens', type=int, help='Maximum tokens')
    execute_parser.add_argument('--no-inputs', dest='show_inputs', action='store_false', help='Hide input prompts')
    execute_parser.add_argument('--no-instructions', dest='show_instructions', action='store_false', help='Hide system instructions')
    execute_parser.add_argument('--no-responses', dest='show_responses', action='store_false', help='Hide responses')
    
    # Catalog command
    catalog_parser = subparsers.add_parser('catalog', help='Manage template catalog')
    catalog_parser.add_argument('catalog_action', choices=['generate', 'list', 'info'], help='Catalog action')
    catalog_parser.add_argument('--stage', help='Filter by stage')
    catalog_parser.add_argument('--output', '-o', help='Output file path')
    
    # Create command
    create_parser = subparsers.add_parser('create', help='Create a new template')
    create_parser.add_argument('name', help='Template name')
    create_parser.add_argument('interpretation', help='Template interpretation')
    create_parser.add_argument('transformation', help='Template transformation')
    create_parser.add_argument('--stage', default='stage1', help='Target stage')
    create_parser.add_argument('--category', help='Template category')
    create_parser.add_argument('--testprompt', help='Test prompt')
    
    # Validate command
    validate_parser = subparsers.add_parser('validate', help='Validate a template file')
    validate_parser.add_argument('file', help='Template file path')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    if args.command == 'execute':
        asyncio.run(execute_template(args))
    elif args.command == 'catalog':
        manage_catalog(args)
    elif args.command == 'create':
        create_template(args)
    elif args.command == 'validate':
        validate_template(args)


if __name__ == '__main__':
    main()
