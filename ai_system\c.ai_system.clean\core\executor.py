"""
Template execution engine for the AI template system.

This module provides the core execution engine for running templates
against multiple LLM models with support for sequences, chains, and
structured output.
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable

from .models import (
    ExecutorConfig, ModelResponse, InstructionResult, 
    ExecutionResults, TemplateCatalog
)
from .config import Config
from .catalog import CatalogManager


class TemplateExecutor:
    """Core template execution engine."""
    
    def __init__(self):
        self.config = Config()
        self.catalog_manager = CatalogManager()
        self._configure_litellm()
    
    def _configure_litellm(self):
        """Configure LiteLLM for multi-model support."""
        try:
            import litellm
            self.config.configure_litellm()
            self.litellm = litellm
        except ImportError:
            print("Warning: LiteLLM not available. Install with: pip install litellm")
            self.litellm = None
    
    async def execute_sequence(self, config: ExecutorConfig) -> ExecutionResults:
        """Execute a template sequence with the given configuration."""
        start_time = time.time()
        results = []
        total_cost = 0.0
        
        # Load catalog if needed
        catalog = self.catalog_manager.load_catalog()
        
        # Process each step in the sequence
        current_input = config.user_prompt
        
        for i, (step_id, template_data) in enumerate(config.sequence_steps):
            step_start_time = time.time()
            
            # Extract system instruction
            system_instruction = config.system_instruction_extractor(template_data)
            
            # Get template metadata
            template_metadata = catalog.templates.get(step_id.split('-')[0], None)
            title = template_metadata.title if template_metadata else f"Step {step_id}"
            stage = template_metadata.stage if template_metadata else "unknown"
            
            # Execute step across all models
            step_responses = {}
            for model in config.models:
                try:
                    response = await self._execute_single_model(
                        model=model,
                        system_instruction=system_instruction,
                        user_input=current_input,
                        temperature=config.temperature,
                        max_tokens=config.max_tokens
                    )
                    step_responses[model] = response
                    total_cost += response.cost or 0.0
                    
                except Exception as e:
                    print(f"Error executing {model} for step {step_id}: {e}")
                    step_responses[model] = ModelResponse(
                        model=model,
                        content=f"Error: {str(e)}",
                        tokens_used=0,
                        cost=0.0,
                        response_time=0.0
                    )
            
            # Create instruction result
            step_time = time.time() - step_start_time
            instruction_result = InstructionResult(
                instruction=system_instruction,
                step=step_id,
                title=title,
                stage=stage,
                responses=step_responses,
                execution_time=step_time
            )
            
            results.append(instruction_result)
            
            # Update input for chain mode
            if config.chain_mode and step_responses:
                # Use first successful response as input for next step
                for response in step_responses.values():
                    if response.content and not response.content.startswith("Error:"):
                        current_input = response.content
                        break
            
            # Display progress if configured
            if config.show_responses:
                self._display_step_results(instruction_result, i + 1)
        
        total_time = time.time() - start_time
        
        return ExecutionResults(
            user_prompt=config.user_prompt,
            sequence_id=config.sequence_id,
            results=results,
            total_cost=total_cost,
            total_time=total_time,
            timestamp=datetime.now()
        )
    
    async def _execute_single_model(
        self, 
        model: str, 
        system_instruction: str, 
        user_input: str,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None
    ) -> ModelResponse:
        """Execute a single model call."""
        if not self.litellm:
            # Fallback for when LiteLLM is not available
            return ModelResponse(
                model=model,
                content=f"Mock response for {model}: {user_input[:100]}...",
                tokens_used=100,
                cost=0.01,
                response_time=1.0
            )
        
        start_time = time.time()
        
        try:
            # Get model parameters
            model_params = self.config.get_model_params(model)
            
            # Prepare messages
            messages = [
                {"role": "system", "content": system_instruction},
                {"role": "user", "content": user_input}
            ]
            
            # Add optional parameters
            kwargs = {
                "model": model_params["model"],
                "messages": messages
            }
            
            if temperature is not None:
                kwargs["temperature"] = temperature
            if max_tokens is not None:
                kwargs["max_tokens"] = max_tokens
            
            # Make API call
            response = await self.litellm.acompletion(**kwargs)
            
            # Extract response data
            content = response.choices[0].message.content
            tokens_used = response.usage.total_tokens if response.usage else 0
            
            # Calculate cost (if available)
            cost = 0.0
            try:
                cost = self.litellm.completion_cost(response)
            except:
                pass
            
            response_time = time.time() - start_time
            
            return ModelResponse(
                model=model,
                content=content,
                tokens_used=tokens_used,
                cost=cost,
                response_time=response_time
            )
            
        except Exception as e:
            response_time = time.time() - start_time
            return ModelResponse(
                model=model,
                content=f"Error: {str(e)}",
                tokens_used=0,
                cost=0.0,
                response_time=response_time
            )
    
    def _display_step_results(self, result: InstructionResult, step_number: int):
        """Display results for a single step."""
        print(f"\n{'='*60}")
        print(f"Step {step_number}: {result.title} ({result.step})")
        print(f"Stage: {result.stage}")
        print(f"{'='*60}")
        
        for model, response in result.responses.items():
            print(f"\n--- {model} ---")
            print(f"Response: {response.content[:200]}...")
            print(f"Tokens: {response.tokens_used}, Cost: ${response.cost:.4f}, Time: {response.response_time:.2f}s")
    
    def save_results(self, results: ExecutionResults, output_file: str, minified: bool = False):
        """Save execution results to JSON file."""
        # Ensure output directory exists
        import os
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # Convert to dict for JSON serialization
        results_dict = results.dict()
        
        # Write JSON file
        with open(output_file, 'w', encoding='utf-8') as f:
            if minified:
                json.dump(results_dict, f, separators=(',', ':'), default=str)
            else:
                json.dump(results_dict, f, indent=2, default=str)
        
        print(f"Results saved to: {output_file}")
    
    def resolve_sequence(self, sequence_spec: str) -> List[tuple]:
        """Resolve sequence specification to template steps."""
        catalog = self.catalog_manager.load_catalog()
        
        # Handle simple template ID
        if sequence_spec.isdigit():
            template_id = sequence_spec
            if template_id in catalog.templates:
                template_data = catalog.templates[template_id]
                return [(template_id, template_data.dict())]
        
        # Handle sequence with steps (e.g., "1010:a-c")
        if ':' in sequence_spec:
            base_id, step_range = sequence_spec.split(':')
            if '-' in step_range:
                start_step, end_step = step_range.split('-')
                steps = []
                for i in range(ord(start_step), ord(end_step) + 1):
                    step = chr(i)
                    template_id = f"{base_id}-{step}"
                    if template_id in catalog.templates:
                        template_data = catalog.templates[template_id]
                        steps.append((template_id, template_data.dict()))
                return steps
        
        # Handle sequence from catalog
        if sequence_spec in catalog.sequences:
            sequence_steps = catalog.sequences[sequence_spec]
            steps = []
            for seq_step in sequence_steps:
                template_id = seq_step.template_id
                if template_id in catalog.templates:
                    template_data = catalog.templates[template_id]
                    steps.append((template_id, template_data.dict()))
            return steps
        
        raise ValueError(f"Could not resolve sequence specification: {sequence_spec}")
    
    def create_config(
        self,
        sequence_spec: str,
        user_prompt: str,
        models: Optional[List[str]] = None,
        chain_mode: bool = True,
        **kwargs
    ) -> ExecutorConfig:
        """Create executor configuration from parameters."""
        if models is None:
            models = self.config.DEFAULT_MODELS
        
        # Resolve sequence steps
        sequence_steps = self.resolve_sequence(sequence_spec)
        
        # Create system instruction extractor
        def extract_instruction(template_data):
            if isinstance(template_data, dict):
                return template_data.get('raw', str(template_data))
            return str(template_data)
        
        return ExecutorConfig(
            sequence_steps=sequence_steps,
            user_prompt=user_prompt,
            sequence_id=sequence_spec,
            models=models,
            chain_mode=chain_mode,
            system_instruction_extractor=extract_instruction,
            **kwargs
        )
