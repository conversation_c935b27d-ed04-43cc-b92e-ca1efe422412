"""
Template validation engine for the AI template system.

This module provides comprehensive validation of templates against the
canonical three-part structure and compliance rules.
"""

import re
from typing import List, Dict, Any, Optional, Tuple
from .models import ValidationResult, TemplateMetadata


class TemplateValidator:
    """Comprehensive template validation engine."""
    
    # Forbidden language patterns
    FORBIDDEN_PATTERNS = {
        "first_person": [
            r"\bI\b", r"\bme\b", r"\bmy\b", r"\bwe\b", r"\bus\b", r"\bour\b",
            r"\bI'll\b", r"\bI'm\b", r"\bI've\b", r"\bwe'll\b", r"\bwe're\b"
        ],
        "conversational": [
            r"\bplease\b", r"\bthank you\b", r"\byou're welcome\b",
            r"\blet's\b", r"\bshall we\b", r"\bwould you like\b",
            r"\bfeel free\b", r"\bdon't hesitate\b"
        ],
        "uncertainty": [
            r"\bmaybe\b", r"\bperhaps\b", r"\bmight\b", r"\bcould\b",
            r"\bpossibly\b", r"\bpotentially\b", r"\blikely\b",
            r"\bseems like\b", r"\bappears to be\b"
        ],
        "questions": [
            r"\bWould you like me to\b", r"\bShould I\b", r"\bDo you want\b",
            r"\?$"  # Questions ending with ?
        ],
        "explanatory": [
            r"\bThis is because\b", r"\bThe reason for this is\b",
            r"\bIn order to\b", r"\bI understand that\b",
            r"\bBased on your request\b", r"\bLet me help you\b"
        ]
    }
    
    # Required structure patterns
    STRUCTURE_PATTERNS = {
        "title": re.compile(r'^\[([^\]]+)\]'),
        "goal_negation": re.compile(r'Your goal is not to \*\*\w+\*\*.*?but to \*\*\w+\*\*'),
        "execute_as": re.compile(r'Execute as:'),
        "transformation": re.compile(r'`\{([^}]+)\}`'),
        "role": re.compile(r'role=([^;]+)'),
        "input": re.compile(r'input=\[([^\]]+)\]'),
        "process": re.compile(r'process=\[([^\]]+)\]'),
        "output": re.compile(r'output=\{([^}]+)\}')
    }
    
    # Generic role patterns to avoid
    GENERIC_ROLES = [
        "assistant", "helper", "ai", "bot", "agent", "processor", 
        "handler", "manager", "expert", "consultant", "advisor"
    ]

    @classmethod
    def validate_template(cls, template_content: str, template_id: str = "") -> ValidationResult:
        """Perform comprehensive template validation."""
        errors = []
        warnings = []
        
        # Basic structure validation
        structure_errors = cls._validate_structure(template_content)
        errors.extend(structure_errors)
        
        # Language validation
        language_errors = cls._validate_language(template_content)
        errors.extend(language_errors)
        
        # Component validation
        component_errors, component_warnings = cls._validate_components(template_content)
        errors.extend(component_errors)
        warnings.extend(component_warnings)
        
        # Calculate compliance score
        total_checks = 20  # Approximate number of validation checks
        compliance_score = max(0.0, 1.0 - (len(errors) / total_checks))
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            template_id=template_id,
            errors=errors,
            warnings=warnings,
            compliance_score=compliance_score
        )

    @classmethod
    def _validate_structure(cls, content: str) -> List[str]:
        """Validate three-part structure."""
        errors = []
        
        # Check for title
        title_match = cls.STRUCTURE_PATTERNS["title"].search(content)
        if not title_match:
            errors.append("Missing title in square brackets [Title]")
        elif not title_match.group(1).strip():
            errors.append("Empty title")
        
        # Check for goal negation pattern
        goal_match = cls.STRUCTURE_PATTERNS["goal_negation"].search(content)
        if not goal_match:
            errors.append("Missing goal negation pattern: 'Your goal is not to **X**, but to **Y**'")
        
        # Check for "Execute as:"
        execute_match = cls.STRUCTURE_PATTERNS["execute_as"].search(content)
        if not execute_match:
            errors.append("Missing 'Execute as:' transition")
        
        # Check for transformation block
        transform_match = cls.STRUCTURE_PATTERNS["transformation"].search(content)
        if not transform_match:
            errors.append("Missing transformation block in backticks `{...}`")
        
        return errors

    @classmethod
    def _validate_language(cls, content: str) -> List[str]:
        """Validate language compliance."""
        errors = []
        
        for category, patterns in cls.FORBIDDEN_PATTERNS.items():
            for pattern in patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    errors.append(f"Forbidden {category} language detected: {pattern}")
        
        return errors

    @classmethod
    def _validate_components(cls, content: str) -> Tuple[List[str], List[str]]:
        """Validate transformation components."""
        errors = []
        warnings = []
        
        transform_match = cls.STRUCTURE_PATTERNS["transformation"].search(content)
        if not transform_match:
            return ["Missing transformation block"], []
        
        transform_content = transform_match.group(1)
        
        # Validate role
        role_match = cls.STRUCTURE_PATTERNS["role"].search(transform_content)
        if not role_match:
            errors.append("Missing role specification")
        else:
            role = role_match.group(1).strip()
            if any(generic in role.lower() for generic in cls.GENERIC_ROLES):
                errors.append(f"Generic role detected: {role}")
            if not re.match(r'^[a-z_]+$', role):
                warnings.append(f"Role should use underscore_case: {role}")
        
        # Validate input
        input_match = cls.STRUCTURE_PATTERNS["input"].search(transform_content)
        if not input_match:
            errors.append("Missing input specification")
        else:
            input_spec = input_match.group(1)
            if ':' not in input_spec:
                errors.append("Input parameters must be typed: [name:type]")
        
        # Validate process
        process_match = cls.STRUCTURE_PATTERNS["process"].search(transform_content)
        if not process_match:
            errors.append("Missing process specification")
        else:
            process_spec = process_match.group(1)
            if '()' not in process_spec:
                warnings.append("Process steps should use function notation: step()")
        
        # Validate output
        output_match = cls.STRUCTURE_PATTERNS["output"].search(transform_content)
        if not output_match:
            errors.append("Missing output specification")
        else:
            output_spec = output_match.group(1)
            if ':' not in output_spec:
                errors.append("Output must be typed: {name:type}")
        
        return errors, warnings

    @classmethod
    def extract_metadata(cls, content: str, file_path: str = "") -> TemplateMetadata:
        """Extract metadata from template content."""
        # Extract title
        title_match = cls.STRUCTURE_PATTERNS["title"].search(content)
        title = title_match.group(1) if title_match else "Unknown"
        
        # Extract interpretation (everything between title and "Execute as:")
        interpretation = ""
        execute_match = cls.STRUCTURE_PATTERNS["execute_as"].search(content)
        if title_match and execute_match:
            start = title_match.end()
            end = execute_match.start()
            interpretation = content[start:end].strip()
        
        # Extract transformation
        transform_match = cls.STRUCTURE_PATTERNS["transformation"].search(content)
        transformation = transform_match.group(0) if transform_match else ""
        
        # Extract keywords from interpretation
        keywords = cls._extract_keywords(interpretation)
        
        # Determine stage from file path or content
        stage = cls._determine_stage(file_path, content)
        
        # Determine category and direction
        category = cls._determine_category(content)
        direction = cls._determine_direction(content)
        
        return TemplateMetadata(
            title=title,
            interpretation=interpretation,
            transformation=transformation,
            keywords=keywords,
            stage=stage,
            category=category,
            direction=direction,
            raw=content,
            file_path=file_path
        )

    @classmethod
    def _extract_keywords(cls, text: str) -> str:
        """Extract semantic keywords from text."""
        # Common semantic keywords in AI templates
        keyword_patterns = [
            r'\b(distill|essence|extract|identify|analyze|synthesize|transform|amplify|clarify|optimize)\b',
            r'\b(precision|coherence|structure|elegant|recursive|adaptive|meta)\b',
            r'\b(maximally|comprehensive|systematic|strategic|tactical)\b'
        ]
        
        keywords = set()
        for pattern in keyword_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            keywords.update(match.lower() for match in matches)
        
        return ", ".join(sorted(keywords))

    @classmethod
    def _determine_stage(cls, file_path: str, content: str) -> str:
        """Determine template stage from file path or content."""
        if "stage1" in file_path or "/1" in file_path:
            return "stage1"
        elif "stage2" in file_path or "/2" in file_path:
            return "stage2"
        elif "stage3" in file_path or "/3" in file_path:
            return "stage3"
        elif "directional" in file_path or "/9" in file_path:
            return "directional"
        
        # Try to determine from template ID in content
        id_match = re.search(r'(\d{4})', content)
        if id_match:
            template_id = int(id_match.group(1))
            if 1000 <= template_id <= 1999:
                return "stage1"
            elif 2000 <= template_id <= 2999:
                return "stage2"
            elif 3000 <= template_id <= 3999:
                return "stage3"
            elif 9000 <= template_id <= 9999:
                return "directional"
        
        return "stage1"  # Default

    @classmethod
    def _determine_category(cls, content: str) -> Optional[str]:
        """Determine template category from content."""
        categories = {
            "extractor": ["extract", "identify", "detect"],
            "classifier": ["classify", "categorize", "determine"],
            "transformer": ["transform", "convert", "metamorphosis"],
            "amplifier": ["amplify", "intensify", "enhance"],
            "distiller": ["distill", "essence", "compress"],
            "synthesizer": ["synthesize", "combine", "merge"],
            "optimizer": ["optimize", "improve", "refine"],
            "analyzer": ["analyze", "examine", "evaluate"]
        }
        
        content_lower = content.lower()
        for category, keywords in categories.items():
            if any(keyword in content_lower for keyword in keywords):
                return category
        
        return None

    @classmethod
    def _determine_direction(cls, content: str) -> Optional[str]:
        """Determine transformation direction from content."""
        expand_keywords = ["amplify", "expand", "enhance", "build", "elaborate"]
        compress_keywords = ["distill", "compress", "reduce", "extract", "minimize"]
        
        content_lower = content.lower()
        
        if any(keyword in content_lower for keyword in expand_keywords):
            return "expand"
        elif any(keyword in content_lower for keyword in compress_keywords):
            return "compress"
        
        return None
