"""
Template catalog management for the AI template system.

This module handles template discovery, cataloging, and organization
across the stage-based architecture.
"""

import os
import json
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

from .models import (
    TemplateCatalog, CatalogMetadata, TemplateMetadata, 
    SequenceStep, StageInfo, TemplateCreationRequest, TemplateCreationResult
)
from .config import Config, PathUtils
from .validation import TemplateValidator


class CatalogManager:
    """Manages template catalogs and stage-based organization."""
    
    def __init__(self):
        self.config = Config()
        self.validator = TemplateValidator()
        
    def load_catalog(self) -> TemplateCatalog:
        """Load and generate complete template catalog."""
        templates = {}
        sequences = {}
        stage_counts = {}
        
        # Process each stage
        for stage_name, stage_config in self.config.STAGE_CONFIG.items():
            stage_dir = self.config.get_stage_directory(stage_name)
            stage_templates, stage_sequences = self._process_stage_directory(stage_dir, stage_name)
            
            templates.update(stage_templates)
            sequences.update(stage_sequences)
            stage_counts[stage_name] = len(stage_templates)
        
        # Create catalog metadata
        catalog_meta = CatalogMetadata(
            level="clean",
            format="md",
            generated_at=datetime.now().isoformat(),
            source_directory=self.config.TEMPLATES_DIR,
            total_templates=len(templates),
            total_sequences=len(sequences),
            stages=stage_counts
        )
        
        return TemplateCatalog(
            catalog_meta=catalog_meta,
            templates=templates,
            sequences=sequences
        )
    
    def _process_stage_directory(self, stage_dir: str, stage_name: str) -> Tuple[Dict[str, TemplateMetadata], Dict[str, List[SequenceStep]]]:
        """Process templates in a stage directory."""
        templates = {}
        sequences = {}
        
        if not os.path.exists(stage_dir):
            PathUtils.ensure_dir_exists(stage_dir)
            return templates, sequences
        
        # Find all .md files
        for file_path in Path(stage_dir).glob("*.md"):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                
                if not content:
                    continue
                
                # Extract template metadata
                metadata = self.validator.extract_metadata(content, str(file_path))
                metadata.stage = stage_name
                
                # Determine template ID from filename
                template_id = self._extract_template_id(file_path.name)
                if template_id:
                    templates[template_id] = metadata
                    
                    # Check if this is part of a sequence
                    sequence_info = self._parse_sequence_info(file_path.name)
                    if sequence_info:
                        seq_id, step, order = sequence_info
                        if seq_id not in sequences:
                            sequences[seq_id] = []
                        
                        sequences[seq_id].append(SequenceStep(
                            template_id=template_id,
                            step=step,
                            order=order,
                            stage=stage_name
                        ))
            
            except Exception as e:
                print(f"Warning: Error processing {file_path}: {e}")
        
        # Sort sequence steps by order
        for seq_steps in sequences.values():
            seq_steps.sort(key=lambda x: x.order)
        
        return templates, sequences
    
    def _extract_template_id(self, filename: str) -> Optional[str]:
        """Extract template ID from filename."""
        # Pattern: 1010-title_extractor.md or 1010-a-title_extractor.md
        match = re.match(r'(\d{4})(?:-[a-z])?-', filename)
        if match:
            return match.group(1)
        
        # Full pattern with step
        match = re.match(r'(\d{4}-[a-z])-', filename)
        if match:
            return match.group(1)
        
        return None
    
    def _parse_sequence_info(self, filename: str) -> Optional[Tuple[str, str, int]]:
        """Parse sequence information from filename."""
        # Pattern: 1010-a-title_extractor.md
        match = re.match(r'(\d{4})-([a-z])-', filename)
        if match:
            seq_id = match.group(1)
            step = match.group(2)
            order = ord(step) - ord('a')
            return seq_id, step, order
        
        return None
    
    def get_stage_info(self, stage: str) -> StageInfo:
        """Get information about a specific stage."""
        stage_config = self.config.get_stage_info(stage)
        if not stage_config:
            raise ValueError(f"Unknown stage: {stage}")
        
        stage_dir = self.config.get_stage_directory(stage)
        template_count = 0
        
        if os.path.exists(stage_dir):
            template_count = len(list(Path(stage_dir).glob("*.md")))
        
        # Calculate next available ID
        next_id = None
        if stage in ["stage1", "stage2", "stage3"]:
            id_range = stage_config["id_range"]
            next_id = self._find_next_available_id(stage_dir, id_range)
        
        return StageInfo(
            stage_name=stage_config["name"],
            id_range=stage_config["id_range"],
            description=stage_config["description"],
            template_count=template_count,
            next_available_id=next_id
        )
    
    def _find_next_available_id(self, stage_dir: str, id_range: Tuple[int, int]) -> int:
        """Find the next available ID in a stage."""
        if not os.path.exists(stage_dir):
            return id_range[0]
        
        used_ids = set()
        for file_path in Path(stage_dir).glob("*.md"):
            template_id = self._extract_template_id(file_path.name)
            if template_id and template_id.isdigit():
                used_ids.add(int(template_id))
        
        # Find first available ID in range
        for i in range(id_range[0], id_range[1] + 1):
            if i not in used_ids:
                return i
        
        raise ValueError(f"No available IDs in range {id_range}")
    
    def create_template(self, request: TemplateCreationRequest) -> TemplateCreationResult:
        """Create a new template in the specified stage."""
        try:
            # Get stage info and next available ID
            stage_info = self.get_stage_info(request.stage)
            
            if request.stage in ["stage1", "stage2", "stage3"]:
                template_id = str(stage_info.next_available_id)
            else:
                # For directional templates, use manual ID assignment
                raise ValueError("Directional templates require manual ID assignment")
            
            # Create template content
            transformation = request.transformation
            if not transformation.startswith('`{'):
                transformation = f"`{transformation}`"
            content = f"[{request.name.title()}] {request.interpretation} Execute as: {transformation}"
            
            # Validate template
            validation_result = self.validator.validate_template(content, template_id)
            
            # Create file path
            stage_dir = self.config.get_stage_directory(request.stage)
            PathUtils.ensure_dir_exists(stage_dir)
            
            filename = f"{template_id}-{request.name.lower().replace(' ', '_')}.md"
            file_path = os.path.join(stage_dir, filename)
            
            # Write template file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # Create test prompt file if provided
            if request.testprompt:
                test_file_path = file_path.replace('.md', '.testprompt')
                with open(test_file_path, 'w', encoding='utf-8') as f:
                    f.write(request.testprompt)
            
            return TemplateCreationResult(
                success=True,
                template_id=template_id,
                file_path=file_path,
                validation_result=validation_result,
                message=f"Template {template_id} created successfully"
            )
            
        except Exception as e:
            return TemplateCreationResult(
                success=False,
                template_id="",
                file_path="",
                validation_result=self.validator.validate_template("", ""),
                message=f"Template creation failed: {str(e)}"
            )
    
    def promote_template(self, template_id: str, from_stage: str, to_stage: str, target_id: Optional[int] = None) -> TemplateCreationResult:
        """Promote a template from one stage to another."""
        try:
            # Load source template
            from_dir = self.config.get_stage_directory(from_stage)
            source_files = list(Path(from_dir).glob(f"{template_id}-*.md"))
            
            if not source_files:
                raise ValueError(f"Template {template_id} not found in {from_stage}")
            
            source_file = source_files[0]
            with open(source_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Determine new ID
            if target_id:
                new_id = str(target_id)
            else:
                to_stage_info = self.get_stage_info(to_stage)
                new_id = str(to_stage_info.next_available_id)
            
            # Create new file in target stage
            to_dir = self.config.get_stage_directory(to_stage)
            PathUtils.ensure_dir_exists(to_dir)
            
            # Extract name from original filename
            name_match = re.search(r'\d{4}(?:-[a-z])?-(.+)\.md$', source_file.name)
            name = name_match.group(1) if name_match else "promoted_template"
            
            new_filename = f"{new_id}-{name}.md"
            new_file_path = os.path.join(to_dir, new_filename)
            
            # Update content with new ID if needed
            updated_content = content  # Could add ID updating logic here
            
            # Validate promoted template
            validation_result = self.validator.validate_template(updated_content, new_id)
            
            # Write new file
            with open(new_file_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            return TemplateCreationResult(
                success=True,
                template_id=new_id,
                file_path=new_file_path,
                validation_result=validation_result,
                message=f"Template promoted from {from_stage} to {to_stage} as {new_id}"
            )
            
        except Exception as e:
            return TemplateCreationResult(
                success=False,
                template_id="",
                file_path="",
                validation_result=self.validator.validate_template("", ""),
                message=f"Template promotion failed: {str(e)}"
            )
    
    def list_templates_by_stage(self, stage: str) -> List[TemplateMetadata]:
        """List all templates in a specific stage."""
        catalog = self.load_catalog()
        return [template for template in catalog.templates.values() if template.stage == stage]
    
    def save_catalog_json(self, output_path: Optional[str] = None) -> str:
        """Save catalog as JSON file."""
        catalog = self.load_catalog()
        
        if not output_path:
            output_path = os.path.join(self.config.TEMPLATES_DIR, "catalog.json")
        
        PathUtils.ensure_dir_exists(os.path.dirname(output_path))
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(catalog.dict(), f, indent=2, default=str)
        
        return output_path
